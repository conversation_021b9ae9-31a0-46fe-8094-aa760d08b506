/**
 * Patient Dashboard Component
 * Main dashboard interface for patients with appointments, health records, and quick actions
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import {
  Calendar,
  Heart,
  FileText,
  Clock,
  User,
  MessageSquare,
  Activity,
  Pill,
  Plus,
  Search,
  Filter,
  Bell,
  TrendingUp
} from 'lucide-react';

import type { RootState, AppDispatch } from '../../store/index';
import DashboardLayout from '../../shared/components/layouts/DashboardLayout';
import MetricCard from '../../shared/components/data-display/MetricCard';
import DataTable from '../ui/DataTable';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/badge';

interface Appointment {
  id: number;
  appointment_id: string;
  doctor: {
    user: {
      full_name: string;
    };
    specialization: string;
  };
  appointment_date: string;
  appointment_time: string;
  appointment_type: string;
  status: string;
  reason_for_visit: string;
}

interface HealthMetrics {
  upcomingAppointments: number;
  activeRecords: number;
  pendingReports: number;
  medications: number;
}

const PatientDashboard: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);

  // State management
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [metrics, setMetrics] = useState<HealthMetrics>({
    upcomingAppointments: 0,
    activeRecords: 0,
    pendingReports: 0,
    medications: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setMetrics({
        upcomingAppointments: 3,
        activeRecords: 12,
        pendingReports: 2,
        medications: 4
      });

      setAppointments([
        {
          id: 1,
          appointment_id: 'APT001',
          doctor: {
            user: { full_name: 'Dr. Sarah Johnson' },
            specialization: 'Cardiology'
          },
          appointment_date: '2025-01-15',
          appointment_time: '10:00',
          appointment_type: 'consultation',
          status: 'scheduled',
          reason_for_visit: 'Heart checkup'
        },
        {
          id: 2,
          appointment_id: 'APT002',
          doctor: {
            user: { full_name: 'Dr. Michael Chen' },
            specialization: 'General Medicine'
          },
          appointment_date: '2025-01-20',
          appointment_time: '14:30',
          appointment_type: 'follow_up',
          status: 'confirmed',
          reason_for_visit: 'Follow-up consultation'
        }
      ]);

      setLoading(false);
    }, 1000);
  }, []);

  // Quick action items
  const quickActions = [
    {
      id: 'book-appointment',
      title: t('patient.bookAppointment'),
      description: t('patient.scheduleNewAppointment'),
      icon: Calendar,
      onClick: () => console.log('Book appointment'),
    },
    {
      id: 'view-records',
      title: t('patient.medicalRecords'),
      description: t('patient.viewHealthRecords'),
      icon: FileText,
      onClick: () => console.log('View records'),
    },
    {
      id: 'medications',
      title: t('patient.medications'),
      description: t('patient.manageMedications'),
      icon: Pill,
      onClick: () => console.log('View medications'),
      badge: '4',
    },
    {
      id: 'contact-doctor',
      title: t('patient.contactDoctor'),
      description: t('patient.sendMessageToDoctor'),
      icon: MessageSquare,
      onClick: () => console.log('Contact doctor'),
    },
  ];

  // Table columns for appointments
  const appointmentColumns = [
    {
      key: 'appointment_date',
      title: t('common.date'),
      sortable: true,
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'appointment_time',
      title: t('common.time'),
      render: (value: string) => (
        <span className="font-mono text-sm">
          {new Date(`2000-01-01T${value}`).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </span>
      ),
    },
    {
      key: 'doctor',
      title: t('common.doctor'),
      sortable: true,
      render: (doctor: any) => (
        <div>
          <p className="font-medium">{doctor.user.full_name}</p>
          <p className="text-sm text-gray-500">{doctor.specialization}</p>
        </div>
      ),
    },
    {
      key: 'appointment_type',
      title: t('common.type'),
      sortable: true,
      render: (value: string) => (
        <Badge variant="outline" className="capitalize">
          {value.replace('_', ' ')}
        </Badge>
      ),
    },
    {
      key: 'status',
      title: t('common.status'),
      sortable: true,
      render: (value: string) => (
        <Badge
          variant={
            value === 'completed'
              ? 'default'
              : value === 'confirmed'
              ? 'secondary'
              : value === 'scheduled'
              ? 'outline'
              : 'destructive'
          }
          className="capitalize"
        >
          {value.replace('_', ' ')}
        </Badge>
      ),
    },
    {
      key: 'reason_for_visit',
      title: t('common.reason'),
      render: (value: string) => (
        <span className="text-sm" title={value}>
          {value.length > 30 ? `${value.substring(0, 30)}...` : value}
        </span>
      ),
    },
  ];

  return (
    <DashboardLayout
      title={t('dashboard.patientDashboard')}
      subtitle={`Welcome, ${user?.full_name || 'Patient'}`}
      description={t('dashboard.patientDashboardDescription')}
      icon={User}
      iconColor="feature-green"
    >
      {/* Health Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard
          label={t('dashboard.upcomingAppointments')}
          value={metrics.upcomingAppointments.toString()}
          icon={Calendar}
          color="blue"
          trend="up"
          change="+1"
          loading={loading}
        />
        <MetricCard
          label={t('dashboard.activeRecords')}
          value={metrics.activeRecords.toString()}
          icon={FileText}
          color="green"
          trend="up"
          change="+2"
          loading={loading}
        />
        <MetricCard
          label={t('dashboard.pendingReports')}
          value={metrics.pendingReports.toString()}
          icon={Activity}
          color="orange"
          trend="down"
          change="-1"
          loading={loading}
        />
        <MetricCard
          label={t('dashboard.medications')}
          value={metrics.medications.toString()}
          icon={Pill}
          color="purple"
          trend="stable"
          loading={loading}
        />
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">{t('dashboard.quickActions')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <div
              key={action.id}
              className="relative p-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg hover:bg-white/20 transition-all cursor-pointer"
              onClick={action.onClick}
            >
              {action.badge && (
                <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
                  {action.badge}
                </div>
              )}
              <action.icon className="w-8 h-8 text-green-400 mb-3" />
              <h4 className="font-medium mb-1">{action.title}</h4>
              <p className="text-sm text-gray-400">{action.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Upcoming Appointments */}
      <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">{t('dashboard.upcomingAppointments')}</h3>
          <div className="flex items-center gap-3">
            <Input
              placeholder={t('common.searchAppointments')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              {t('common.filter')}
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              {t('patient.bookAppointment')}
            </Button>
          </div>
        </div>

        <DataTable
          data={appointments}
          columns={appointmentColumns}
          loading={loading}
          onSearch={(term) => setSearchTerm(term)}
          actions={{
            title: t('common.actions'),
            render: (appointment: Appointment) => (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  {t('common.view')}
                </Button>
                <Button variant="outline" size="sm">
                  {t('common.reschedule')}
                </Button>
                <Button variant="outline" size="sm">
                  {t('common.cancel')}
                </Button>
              </div>
            ),
          }}
        />
      </div>
    </DashboardLayout>
  );
};

export default PatientDashboard;