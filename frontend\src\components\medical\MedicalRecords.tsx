import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/Input';
import { useTheme } from '../../hooks/useTheme';
import useApi from '../../shared/hooks/useApi';
import { useCrud } from '../../shared/hooks/useCrud';
import CRUDModal from '../ui/CRUDModal';
import { medicalRecordsService, type MedicalRecord } from '../../services/medicalService';
import { FileText, Plus, Search, Calendar, User, Stethoscope, Eye, Edit, Download, Filter, AlertCircle, Loader2 } from 'lucide-react';
import { getStatusClass, getPriorityClass } from '../../utils/styleUtils';
const MedicalRecords: React.FC = () => {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(null);

  // Use CRUD hook for medical records
  const {
    items: medicalRecords,
    loading,
    error,
    fetchAll: fetchMedicalRecords,
    create: createMedicalRecord,
    update: updateMedicalRecord,
    delete: deleteMedicalRecord,
  } = useCrud(medicalRecordsService, {
    immediate: true,
  });

  // Search functionality with API
  const {
    data: searchResults,
    loading: searchLoading,
    execute: performSearch,
  } = useApi((query: string, filters: any) =>
    medicalRecordsService.searchMedicalRecords(query, filters), {
    immediate: false,
  }); // CRUD Handlers

const handleViewRecord = (record: MedicalRecord) => {
    alert(`Viewing Medical Record\n\nPatient: ${
    record.patientName
  }\nDiagnosis: ${
    record.diagnosis
  }\nDoctor: ${
    record.doctorName
  }\nDate: ${
    record.date
  }\n\nThis will open the detailed record view.`);
  };
  const handleExportRecord = (record: MedicalRecord) => { // Create a downloadable text file with the record data

const recordData = ` MEDICAL RECORD EXPORT ===================== Patient Information: - Name: ${
    record.patientName
  } - Patient ID: ${
    record.patientId
  } - Date: ${
    record.date
  } - Doctor: ${
    record.doctorName
  } Medical Details: - Diagnosis: ${
    record.diagnosis
  } - Symptoms: ${
    record.symptoms
  } - Treatment: ${
    record.treatment
  } - Status: ${
    record.status
  } - Priority: ${
    record.priority
  } Notes: ${
    record.notes
  } Generated on: ${
    new Date().toLocaleString()
  } `;
  const blob = new Blob([recordData], {
    type: 'text/plain'
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `medical-record-${
    record.id
  }-${
    record.patientName.replace(/\s+/g, '-')
  }.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  };

  // Handle search with debouncing
  useEffect(() => {
    if (searchTerm.trim()) {
      const timeoutId = setTimeout(() => {
        performSearch(searchTerm, { status: selectedFilter !== 'all' ? selectedFilter : undefined });
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [searchTerm, selectedFilter, performSearch]);

  // Additional CRUD handlers
  const handleCreateRecord = () => {
    setSelectedRecord(null);
    setModalMode('create');
    setShowModal(true);
  };

  const handleEditRecord = (record: MedicalRecord) => {
    setSelectedRecord(record);
    setModalMode('edit');
    setShowModal(true);
  };

  const handleSubmitRecord = async (recordData: any) => {
    try {
      if (modalMode === 'create') {
        await createMedicalRecord(recordData);
      } else if (selectedRecord) {
        await updateMedicalRecord(selectedRecord.id, recordData);
      }
      setShowModal(false);
      await fetchMedicalRecords(); // Refresh the list
    } catch (error: any) {
      console.error('Failed to save record:', error);
      throw error;
    }
  };

  const handleDeleteRecord = async (record: MedicalRecord) => {
    try {
      await deleteMedicalRecord(record.id);
      await fetchMedicalRecords(); // Refresh the list
    } catch (error: any) {
      console.error('Failed to delete record:', error);
      throw error;
    }
  }; // Form fields for medical record modal

const recordFormFields = [ {
    key: 'patientName', label: 'Patient Name', type: 'text' as const, required: true
  }, {
    key: 'patientId', label: 'Patient ID', type: 'text' as const, required: true
  }, {
    key: 'doctorName', label: 'Doctor Name', type: 'text' as const, required: true
  }, {
    key: 'date', label: 'Date', type: 'date' as const, required: true
  }, {
    key: 'diagnosis', label: 'Diagnosis', type: 'text' as const, required: true
  }, {
    key: 'symptoms', label: 'Symptoms', type: 'textarea' as

const
  }, {
    key: 'treatment', label: 'Treatment', type: 'textarea' as

const
  }, {
    key: 'status', label: 'Status', type: 'select' as const, required: true, options: [ {
    value: 'active', label: 'Active'
  }, {
    value: 'resolved', label: 'Resolved'
  }, {
    value: 'ongoing', label: 'Ongoing'
  }, {
    value: 'follow_up', label: 'Follow-up Required'
  } ]
  }, {
    key: 'priority', label: 'Priority', type: 'select' as const, required: true, options: [ {
    value: 'low', label: 'Low'
  }, {
    value: 'medium', label: 'Medium'
  }, {
    value: 'high', label: 'High'
  }, {
    value: 'urgent', label: 'Urgent'
  } ]
  }, {
    key: 'notes', label: 'Notes', type: 'textarea' as

const
  } ];

  // Use search results if available, otherwise use all records
  const displayRecords = searchTerm.trim() ? (searchResults || []) : medicalRecords;

  // Filter records based on status filter
  const filteredRecords = displayRecords.filter(record => {
    const matchesFilter = selectedFilter === 'all' || record.status === selectedFilter;
    return matchesFilter;
  });

  // Show loading state
  if (loading && !medicalRecords.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-muted-foreground">{t('common.loading')}...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-2">{t('medical.errorLoadingRecords')}</p>
            <Button onClick={() => fetchMedicalRecords()} variant="outline">
              {t('common.retry')}
            </Button>
          </div>
        </div>
      </div>
    );
  }
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card variant="glass" className="shadow-xl">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
    <FileText className="w-6 h-6 text-white" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary"> {
    t('medical.title')
  }
    </CardTitle>
    <CardDescription className="macos-text-secondary"> {
    t('medical.subtitle')
  }
    </CardDescription> </div> </div>
    <Button variant="glass" onClick={
    handleCreateRecord
  } className="flex items-center gap-2" >
    <Plus className="w-4 h-4" /> {
    t('medical.addRecord')
  }
    </Button> </div>
    </CardHeader>
    </Card> {/* Filters and Search */
  }
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex flex-col md:flex-row gap-4"> <div className="flex-1"> <div className="relative">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 macos-text-tertiary w-4 h-4" />
    <Input variant="glass" placeholder={
    t('common.searchPlaceholder')
  } value={
    searchTerm
  } onChange={(e) => setSearchTerm(e.target.value)
  } className="pl-10" /> </div> </div> <div className="flex items-center gap-3">
    <Filter className="w-4 h-4 macos-text-secondary" /> <select value={
    selectedFilter
  } onChange={(e) => setSelectedFilter(e.target.value)
  } className="glass border-0 rounded-xl px-4 py-2 text-sm macos-text-primary focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" > <option value="all">{
    t('common.all')
  } {
    t('medical.patientRecords')
  }</option> <option value="active">{
    t('common.active')
  }</option> <option value="completed">{
    t('common.completed')
  }</option> <option value="follow-up">{
    t('medical.followUp')
  }</option> </select> </div> </div>
    </CardContent>
    </Card> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('common.total')
  } {
    t('medical.patientRecords')
  }</p> <p className="text-2xl font-bold macos-text-primary">{
    medicalRecords.length
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
    <FileText className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('common.active')
  } {
    t('common.cases')
  }</p> <p className="text-2xl font-bold macos-text-primary"> {
    medicalRecords.filter(r => r.status === 'active').length
  } </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
    <Stethoscope className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('common.high')
  } {
    t('common.priority')
  }</p> <p className="text-2xl font-bold macos-text-primary"> {
    medicalRecords.filter(r => r.priority === 'high').length
  } </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
    <Calendar className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('common.thisMonth')
  }</p> <p className="text-2xl font-bold macos-text-primary"> {
    medicalRecords.filter(r => r.visit_date?.startsWith('2024-12')).length
  } </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
    <User className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card> </div> {/* Medical Records List */
  } <div className="space-y-4"> {
    filteredRecords.map((record) => (
    <Card key={
    record.id
  } variant="glass" className="hover:glass-hover macos-transition">
    <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center gap-4 mb-4"> <h3 className="text-lg font-semibold macos-text-primary"> {
    record.patient?.user?.full_name || 'Unknown Patient'
  } </h3>
    <Badge className={`${
    getStatusClass(record.status)
  } rounded-full px-3 py-1`
  }> {
    record.status
  }
    </Badge>
    <Badge className={`${
    getPriorityClass(record.priority)
  } rounded-full px-3 py-1`
  }> {
    record.priority
  } priority
    </Badge> </div> <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4"> <div className="space-y-2"> <p className="text-sm macos-text-secondary flex items-center gap-2">
    <User className="w-4 h-4" /> {
    t('common.patientId')
  }: <span className="macos-text-primary font-medium">{
    record.patient?.patient_id || 'N/A'
  }</span> </p> <p className="text-sm macos-text-secondary flex items-center gap-2">
    <Stethoscope className="w-4 h-4" /> {
    t('common.doctor')
  }: <span className="macos-text-primary font-medium">{
    record.doctor?.full_name || 'Unknown Doctor'
  }</span> </p> <p className="text-sm macos-text-secondary flex items-center gap-2">
    <Calendar className="w-4 h-4" /> {
    t('common.date')
  }: <span className="macos-text-primary font-medium">{
    record.visit_date
  }</span> </p> </div> <div className="space-y-2"> <p className="text-sm macos-text-secondary"> {
    t('medical.diagnosis')
  }: <span className="macos-text-primary font-medium">{
    record.diagnosis
  }</span> </p> <p className="text-sm macos-text-secondary"> {
    t('common.symptoms')
  }: <span className="macos-text-primary">{
    record.symptoms
  }</span> </p> </div> </div> <div className="mb-4 p-4 glass-subtle rounded-xl"> <p className="text-sm font-semibold macos-text-primary mb-2">{
    t('medical.treatment')
  }:</p> <p className="text-sm macos-text-secondary">{
    record.treatment
  }</p> </div> <div className="p-4 glass-subtle rounded-xl"> <p className="text-sm font-semibold macos-text-primary mb-2">{
    t('medical.notes')
  }:</p> <p className="text-sm macos-text-secondary">{
    record.notes
  }</p> </div> </div> <div className="flex flex-col gap-2 ml-6">
    <Button variant="glass" size="sm" className="flex items-center gap-2" onClick={() => handleViewRecord(record)
  } >
    <Eye className="w-4 h-4" /> {
    t('common.view')
  }
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2" onClick={() => handleEditRecord(record)
  } >
    <Edit className="w-4 h-4" /> {
    t('common.edit')
  }
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2" onClick={() => handleExportRecord(record)
  } >
    <Download className="w-4 h-4" /> {
    t('common.export')
  }
    </Button> </div> </div>
    </CardContent>
    </Card> ))
  } </div> {
    filteredRecords.length === 0 && (
    <Card variant="glass">
    <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <FileText className="w-8 h-8 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2">{
    t('messages.noData')
  }</h3> <p className="macos-text-secondary"> {
    searchTerm ? t('common.adjustSearchCriteria') : t('medical.startByAddingRecord')
  } </p>
    </CardContent>
    </Card> )
  } {/* CRUD Modal */
  }
    <CRUDModal isOpen={
    showModal
  } onClose={() => setShowModal(false)
  } onSubmit={
    handleSubmitRecord
  } title={
    modalMode === 'create' ? 'Add New Medical Record' : 'Edit Medical Record'
  } fields={
    recordFormFields
  } initialData={
    selectedRecord || {
  }
  } mode={
    modalMode
  } /> </div> </div> );
  };
  export default MedicalRecords;
