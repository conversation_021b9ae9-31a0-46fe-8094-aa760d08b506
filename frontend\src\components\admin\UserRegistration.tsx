import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    useDispatch, useSelector
  } from 'react-redux';
  import {
    Eye, EyeOff, User, Mail, Lock, Phone, UserPlus, Shield
  } from 'lucide-react';
  import {
    Button
  } from '../ui/Button';
  import {
    Input
  } from '../ui/Input';
  import {
    Card, CardContent, CardDescription, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Badge
  } from '../ui/badge';
  import {
    useTheme
  } from '../../hooks/useTheme';
import type { AppDispatch, RootState } from '../../store/index';
  import type {
    RegisterData
  } from '../../types/auth';
  const UserRegistration: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const {
    isDark
  } =
  useTheme();
  const dispatch = useDispatch
    <AppDispatch>();
  const {
    user: currentUser
  } =
  useSelector((state: RootState) => state.auth);
  const [isLoading, setIsLoading] =
  useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPassword, setShowPassword] =
  useState(false);
  const [showConfirmPassword, setShowConfirmPassword] =
  useState(false);
  const [formData, setFormData] =
  useState({
    username: '', email: '', password: '', password_confirm: '', first_name: '', last_name: '', role: 'patient' as const, phone_number: '',
  }); // Check if current user is admin

  const isAdmin = currentUser?.role === 'admin' || (currentUser as any)?.is_admin;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear errors when user starts typing
    if (error) setError(null);
  if (success) setSuccess(null);
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
  setError(null);
  setSuccess(null);
  if (formData.password !== formData.password_confirm) {
    setError(t('register.passwordMismatch'));
  return;
  }
  if (!isAdmin) {
    setError('Only administrators can register new users');
  return;
  } setIsLoading(true);
  try { // Create username from email if not provided

const username = formData.username || formData.email.split('@')[0];
  const registerData: RegisterData = {
    username, email: formData.email, password: formData.password, password_confirm: formData.password_confirm, first_name: formData.first_name, last_name: formData.last_name, role: formData.role, phone_number: formData.phone_number || undefined,
  }; // Call API directly since this is admin-only functionality

const response = await fetch('/api/auth/register/', {
    method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${
    localStorage.getItem('token')
  }`,
  }, body: JSON.stringify(registerData),
  });
  if (response.ok) {
    setSuccess(`User ${
    formData.first_name
  } ${formData.last_name} registered successfully`);

      // Reset form
      setFormData({
        username: '',
        email: '',
        password: '',
        password_confirm: '',
        first_name: '',
        last_name: '',
        role: 'patient',
        phone_number: '',
      });
  } else {
    const errorData = await response.json();
  setError(errorData.error || 'Registration failed');
  }
  } catch (error) {
    setError('Network error. Please try again.');
  } finally {
    setIsLoading(false);
  }
  };
  const roleOptions = [ {
    value: 'admin', label: t('register.roles.admin')
  }, {
    value: 'doctor', label: t('register.roles.doctor')
  }, {
    value: 'nurse', label: t('register.roles.nurse')
  }, {
    value: 'patient', label: t('register.roles.patient')
  }, {
    value: 'receptionist', label: t('register.roles.receptionist')
  } ];
  if (!isAdmin) {
    return (
    <Card className="glass border-0 shadow-xl max-w-md mx-auto">
    <CardContent className="p-8 text-center">
    <Shield className="w-16 h-16 text-red-500 mx-auto mb-4" /> <h3 className="text-lg font-semibold macos-text-primary mb-2">Access Denied</h3> <p className="macos-text-secondary"> Only administrators can register new users in the hospital system. </p>
    </CardContent>
    </Card> );
  }
  return ( <div className="max-w-2xl mx-auto space-y-6">
    <Card className="glass border-0 shadow-xl">
    <CardHeader className="text-center pb-6"> <div className="flex items-center justify-center mb-4"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
    <UserPlus className="w-6 h-6 text-white" /> </div> </div>
    <CardTitle className="text-2xl font-bold macos-text-primary"> Register New User
    </CardTitle>
    <CardDescription className="macos-text-secondary"> Add a new user to the hospital management system
    </CardDescription>
    <Badge className="status-info mx-auto">
    <Shield className="w-3 h-3 mr-1" /> Admin Only
    </Badge>
    </CardHeader>
    <CardContent className="space-y-6"> {
    error && ( <div className="p-4 bg-red-50 border rounded-lg"> <p className="text-rose-700 dark:text-rose-400 text-sm">{
    error
  }</p> </div> )
  } {
    success && ( <div className="p-4 bg-green-50 border rounded-lg"> <p className="text-emerald-700 dark:text-emerald-400 text-sm">{
    success
  }</p> </div> )
  } <form onSubmit={
    handleSubmit
  } className="space-y-6"> {/* Personal Information */
  } <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="block text-sm font-semibold macos-text-primary mb-2"> {
    t('register.firstName')
  } </label>
    <Input type="text" name="first_name" value={
    formData.first_name
  } onChange={
    handleInputChange
  } placeholder={
    t('register.firstNamePlaceholder')
  } required className="w-full h-12" variant="glass" /> </div> <div> <label className="block text-sm font-semibold macos-text-primary mb-2"> {
    t('register.lastName')
  } </label>
    <Input type="text" name="last_name" value={
    formData.last_name
  } onChange={
    handleInputChange
  } placeholder={
    t('register.lastNamePlaceholder')
  } required className="w-full h-12" variant="glass" /> </div> </div> {/* Username */
  } <div> <label className="block text-sm font-semibold macos-text-primary mb-2">
    <User className="w-4 h-4 inline mr-2" /> {
    t('register.username')
  } </label>
    <Input type="text" name="username" value={
    formData.username
  } onChange={
    handleInputChange
  } placeholder={
    t('register.usernamePlaceholder')
  } className="w-full h-12" variant="glass" /> <p className="text-xs macos-text-secondary mt-1"> {
    t('register.usernameHint')
  } </p> </div> {/* Role Selection */
  } <div> <label className="block text-sm font-semibold macos-text-primary mb-2"> {
    t('register.role')
  } </label> <select name="role" value={
    formData.role
  } onChange={
    handleInputChange
  } className="w-full h-12 px-4 glass border border-border/50 dark:border-gray-700/50 rounded-xl macos-text-primary bg-background/50 dark:bg-gray-800/50" required > {
    roleOptions.map((option) => ( <option key={
    option.value
  } value={
    option.value
  }> {
    option.label
  } </option> ))
  } </select> </div> {/* Contact Information */
  } <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="block text-sm font-semibold macos-text-primary mb-2">
    <Mail className="w-4 h-4 inline mr-2" /> {
    t('register.email')
  } </label>
    <Input type="email" name="email" value={
    formData.email
  } onChange={
    handleInputChange
  } placeholder={
    t('register.emailPlaceholder')
  } required className="w-full h-12" variant="glass" /> </div> <div> <label className="block text-sm font-semibold macos-text-primary mb-2">
    <Phone className="w-4 h-4 inline mr-2" /> {
    t('register.phone')
  } </label>
    <Input type="tel" name="phone_number" value={
    formData.phone_number
  } onChange={
    handleInputChange
  } placeholder={
    t('register.phonePlaceholder')
  } className="w-full h-12" variant="glass" /> </div> </div> {/* Password Fields */
  } <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="block text-sm font-semibold macos-text-primary mb-2">
    <Lock className="w-4 h-4 inline mr-2" /> {
    t('register.password')
  } </label> <div className="relative">
    <Input type={
    showPassword ? 'text' : 'password'
  } name="password" value={
    formData.password
  } onChange={
    handleInputChange
  } placeholder={
    t('register.passwordPlaceholder')
  } required className="w-full h-12 pr-12" variant="glass" /> <button type="button" onClick={() => setShowPassword(!showPassword)
  } className="absolute right-3 top-1/2 transform -translate-y-1/2 macos-text-tertiary hover:macos-text-secondary macos-transition" > {
    showPassword ?
    <EyeOff className="w-5 h-5" /> :
    <Eye className="w-5 h-5" />
  } </button> </div> </div> <div> <label className="block text-sm font-semibold macos-text-primary mb-2">
    <Lock className="w-4 h-4 inline mr-2" /> {
    t('register.confirmPassword')
  } </label> <div className="relative">
    <Input type={
    showConfirmPassword ? 'text' : 'password'
  } name="password_confirm" value={
    formData.password_confirm
  } onChange={
    handleInputChange
  } placeholder={
    t('register.confirmPasswordPlaceholder')
  } required className="w-full h-12 pr-12" variant="glass" /> <button type="button" onClick={() => setShowConfirmPassword(!showConfirmPassword)
  } className="absolute right-3 top-1/2 transform -translate-y-1/2 macos-text-tertiary hover:macos-text-secondary macos-transition" > {
    showConfirmPassword ?
    <EyeOff className="w-5 h-5" /> :
    <Eye className="w-5 h-5" />
  } </button> </div> </div> </div> {/* Submit Button */
  }
    <Button type="submit" disabled={
    isLoading
  } className="w-full h-12 text-base font-semibold" variant="glass" > <div className="flex items-center gap-2"> {
    isLoading ? ( <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" /> ) : (
    <UserPlus className="w-5 h-5" /> )
  } {
    isLoading ? t('common.loading') : 'Register User'
  } </div>
    </Button> </form>
    </CardContent>
    </Card> </div> );
  };
  export default UserRegistration;
