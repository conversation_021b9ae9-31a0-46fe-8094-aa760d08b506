/**
 * Department Management Component
 * Example of using the new departmentService and doctorService
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/Input';
import useApi from '../../shared/hooks/useApi';
import { useCrud } from '../../shared/hooks/useCrud';
import departmentService, { type Department } from '../../services/departmentService';
import doctorService from '../../services/doctorService';
import { 
  Building2, 
  Users, 
  Phone, 
  Mail, 
  MapPin, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Loader2,
  AlertCircle 
} from 'lucide-react';

const DepartmentManagement: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);

  // Use CRUD hook for departments
  const {
    items: departments,
    loading,
    error,
    fetchAll: fetchDepartments,
    create: createDepartment,
    update: updateDepartment,
    remove: deleteDepartment,
  } = useCrud(departmentService, {
    immediate: true,
  });

  // Fetch doctors for selected department
  const {
    data: departmentDoctors,
    loading: doctorsLoading,
    execute: fetchDepartmentDoctors,
  } = useApi((departmentId: number) => 
    departmentService.getDepartmentDoctors(departmentId), {
    immediate: false,
  });

  // Fetch department statistics
  const {
    data: departmentStats,
    loading: statsLoading,
    execute: fetchDepartmentStats,
  } = useApi((departmentId: number) => 
    departmentService.getDepartmentStats(departmentId), {
    immediate: false,
  });

  // Filter departments based on search
  const filteredDepartments = departments.filter((dept: Department) =>
    dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle department selection
  const handleDepartmentSelect = (department: Department) => {
    setSelectedDepartment(department);
    fetchDepartmentDoctors(department.id);
    fetchDepartmentStats(department.id);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-muted-foreground">{t('common.loading')}...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-2">{t('departments.errorLoading')}</p>
          <Button onClick={() => fetchDepartments()} variant="outline">
            {t('common.retry')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <Card variant="glass" className="shadow-xl">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold macos-text-primary">
                    {t('departments.title')}
                  </CardTitle>
                  <CardDescription className="macos-text-secondary">
                    {t('departments.subtitle')}
                  </CardDescription>
                </div>
              </div>
              <Button className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                {t('departments.addNew')}
              </Button>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Departments List */}
          <div className="lg:col-span-1">
            <Card variant="glass" className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg font-semibold">
                  {t('departments.allDepartments')}
                </CardTitle>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder={t('departments.searchPlaceholder')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {filteredDepartments.map((department: Department) => (
                  <div
                    key={department.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                      selectedDepartment?.id === department.id
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700'
                    }`}
                    onClick={() => handleDepartmentSelect(department)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium macos-text-primary">
                          {department.name}
                        </h3>
                        <p className="text-sm macos-text-secondary">
                          {department.description}
                        </p>
                      </div>
                      <Badge variant="secondary">
                        {department.doctor_count || 0} {t('departments.doctors')}
                      </Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Department Details */}
          <div className="lg:col-span-2">
            {selectedDepartment ? (
              <div className="space-y-6">
                {/* Department Info */}
                <Card variant="glass" className="shadow-lg">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-xl font-bold">
                        {selectedDepartment.name}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="macos-text-secondary">
                      {selectedDepartment.description}
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedDepartment.location && (
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{selectedDepartment.location}</span>
                        </div>
                      )}
                      {selectedDepartment.phone_number && (
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{selectedDepartment.phone_number}</span>
                        </div>
                      )}
                      {selectedDepartment.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{selectedDepartment.email}</span>
                        </div>
                      )}
                    </div>

                    {/* Department Statistics */}
                    {departmentStats && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-blue-600">
                            {departmentStats.total_doctors}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {t('departments.totalDoctors')}
                          </p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-green-600">
                            {departmentStats.total_patients}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {t('departments.totalPatients')}
                          </p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-purple-600">
                            {departmentStats.appointments_today}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {t('departments.appointmentsToday')}
                          </p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-orange-600">
                            {departmentStats.average_rating?.toFixed(1) || 'N/A'}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {t('departments.averageRating')}
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Department Doctors */}
                <Card variant="glass" className="shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold">
                      {t('departments.doctors')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {doctorsLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin" />
                      </div>
                    ) : departmentDoctors && departmentDoctors.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {departmentDoctors.map((doctor: any) => (
                          <div
                            key={doctor.id}
                            className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                          >
                            <h4 className="font-medium macos-text-primary">
                              {doctor.user?.full_name || doctor.name}
                            </h4>
                            <p className="text-sm macos-text-secondary">
                              {doctor.specialization}
                            </p>
                            {doctor.experience_years && (
                              <p className="text-xs text-muted-foreground">
                                {doctor.experience_years} {t('departments.yearsExperience')}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-center text-muted-foreground py-8">
                        {t('departments.noDoctors')}
                      </p>
                    )}
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card variant="glass" className="shadow-lg">
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Building2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      {t('departments.selectDepartment')}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DepartmentManagement;
