import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/Input';
import { useTheme } from '../../hooks/useTheme';
import useApi from '../../shared/hooks/useApi';
import { useCrud } from '../../shared/hooks/useCrud';
import { getStatusClass } from '../../utils/styleUtils';
import { medicalNotesService, type MedicalNote } from '../../services/medicalService';
import { FileText, Plus, Search, Calendar, User, Edit, Save, X, Eye, Filter, Loader2, AlertCircle } from 'lucide-react';
  const MedicalNotes: React.FC = () => {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPatient, setFilterPatient] = useState('all');
  const [isCreating, setIsCreating] =
  useState(false);
  const [editingNote, setEditingNote] = useState<number | null>(null);
  const [newNote, setNewNote] = useState({
    patientId: '',
    title: '',
    content: '',
    category: 'general' as const
  });

  // Use CRUD hook for medical notes
  const {
    items: medicalNotes,
    loading,
    error,
    fetchAll: fetchNotes,
    create: createNote,
    update: updateNote,
    delete: deleteNote,
  } = useCrud(medicalNotesService, {
    immediate: true,
  });

  // Search functionality with API
  const {
    data: searchResults,
    loading: searchLoading,
    execute: performSearch,
  } = useApi((query: string, filters: any) =>
    medicalNotesService.searchNotes(query, filters), {
    immediate: false,
  });

  // Handle search with debouncing
  useEffect(() => {
    if (searchTerm.trim()) {
      const timeoutId = setTimeout(() => {
        performSearch(searchTerm, {
          patient: filterPatient !== 'all' ? filterPatient : undefined
        });
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [searchTerm, filterPatient, performSearch]);

  // Use search results if available, otherwise use all notes
  const displayNotes = searchTerm.trim() ? (searchResults || []) : medicalNotes;

  // Filter notes based on patient filter
  const filteredNotes = displayNotes.filter(note => {
    const matchesFilter = filterPatient === 'all' || note.patient?.id?.toString() === filterPatient;
    return matchesFilter;
  });

  // Show loading state
  if (loading && !medicalNotes.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-muted-foreground">{t('common.loading')}...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-2">{t('medical.errorLoadingNotes')}</p>
            <Button onClick={() => fetchNotes()} variant="outline">
              {t('common.retry')}
            </Button>
          </div>
        </div>
      </div>
    );
  }



  const patients = [ {
    id: 1, name: 'Sarah Johnson'
  }, {
    id: 2, name: 'Michael Brown'
  }, {
    id: 3, name: 'Emily Davis'
  }, {
    id: 4, name: 'Robert Wilson'
  } ];
  const categories = [ {
    value: 'general', label: 'General'
  }, {
    value: 'consultation', label: 'Consultation'
  }, {
    value: 'follow-up', label: 'Follow-up'
  }, {
    value: 'chronic-care', label: 'Chronic Care'
  }, {
    value: 'post-operative', label: 'Post-operative'
  }, {
    value: 'emergency', label: 'Emergency'
  } ];
  const getCategoryClass = (category: string) => {
    switch (category) {
    case 'consultation': return 'status-info-bg';
  case 'follow-up': return 'status-success-bg';
  case 'chronic-care': return 'role-admin';
  case 'post-operative': return 'role-patient';
  case 'emergency': return 'status-error-bg';
  default: return 'bg-muted text-muted-foreground rounded-full px-3 py-1 text-xs font-medium';
  }
  };
  // This filtering is now handled above in the displayNotes logic
  // const filteredNotes = displayNotes; // Already filtered
  const handleCreateNote = () => { // Handle note creation logic here // TODO: Handle action setIsCreating(false);
  setNewNote({
    patientId: '', title: '', content: '', category: 'general'
  });
  };
  const handleEditNote = (noteId: number) => {
    setEditingNote(noteId);
  };
  const handleSaveEdit = (noteId: number) => { // Handle note editing logic here // TODO: Handle action setEditingNote(null);
  };
  const renderNoteCard = (note: any) => (
    <Card key={
    note.id
  } className="glass border-0 shadow-lg hover:glass-hover macos-transition mb-4">
    <CardContent className="p-6"> <div className="flex items-start justify-between mb-4"> <div className="flex-1"> <div className="flex items-center justify-between mb-3"> <div className="flex items-center gap-3"> <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
    <FileText className="w-5 h-5 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary">{
    note.title
  }</h3> </div> <div className="flex space-x-2">
    <Badge className={
    getCategoryClass(note.category)
  }> {
    note.category.replace('-', ' ')
  }
    </Badge>
    <Badge className={
    getStatusClass(note.status)
  }> {
    note.status
  }
    </Badge> </div> </div> <div className="flex items-center space-x-4 mb-4 text-sm macos-text-secondary"> <div className="flex items-center space-x-2">
    <User className="w-4 h-4 macos-text-tertiary" /> <span>{
    note.patient?.user?.full_name || 'Unknown Patient'
  }</span> </div> <div className="flex items-center space-x-2">
    <Calendar className="w-4 h-4 macos-text-tertiary" /> <span>{
    new Date(note.created_at).toLocaleDateString()
  } at {
    new Date(note.created_at).toLocaleTimeString()
  }</span> </div> </div> {
    editingNote === note.id ? ( <div className="space-y-3"> <textarea defaultValue={
    note.content
  } className="glass border-0 rounded-xl w-full p-4 macos-text-primary focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" rows={6
  } /> <div className="flex space-x-2">
    <Button variant="glass" size="sm" onClick={() => handleSaveEdit(note.id)
  } className="flex items-center gap-2">
    <Save className="w-4 h-4" /> Save
    </Button>
    <Button variant="glass" size="sm" onClick={() => setEditingNote(null)
  } className="flex items-center gap-2">
    <X className="w-4 h-4" /> Cancel
    </Button> </div> </div> ) : ( <> <div className="glass-subtle border border-indigo-200/50 dark:border-indigo-800/50 rounded-xl p-4 mb-4"> <p className="text-indigo-800 dark:text-indigo-400 leading-relaxed">{
    note.content
  }</p> </div> <div className="flex flex-wrap gap-2"> {
    note.tags.map((tag: string, index: number) => (
    <Badge key={
    index
  } variant="outline" className="text-xs rounded-full px-2 py-1"> #{
    tag
  }
    </Badge> ))
  } </div> </> )
  } </div> {
    editingNote !== note.id && ( <div className="flex flex-col space-y-2 ml-6">
    <Button variant="glass" size="sm" onClick={() => handleEditNote(note.id)
  } className="flex items-center gap-2">
    <Edit className="w-4 h-4" /> Edit
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Eye className="w-4 h-4" /> View Full
    </Button> </div> )
  } </div>
    </CardContent>
    </Card> );
  const renderCreateForm = () => (
    <Card className="mb-6">
    <CardHeader>
    <CardTitle>Create New Medical Note
    </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4"> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="block text-sm font-medium text-foreground mb-1">Patient</label> <select value={
    newNote.patientId
  } onChange={(e) => setNewNote({ ...newNote, patientId: e.target.value
  })
  } className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" > <option value="">Select Patient</option> {
    patients.map(patient => ( <option key={
    patient.id
  } value={
    patient.id
  }>{
    patient.name
  }</option> ))
  } </select> </div> <div> <label className="block text-sm font-medium text-foreground mb-1">Category</label> <select value={
    newNote.category
  } onChange={(e) => setNewNote({ ...newNote, category: e.target.value
  })
  } className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" > {
    categories.map(category => ( <option key={
    category.value
  } value={
    category.value
  }>{
    category.label
  }</option> ))
  } </select> </div> </div> <div> <label className="block text-sm font-medium text-foreground mb-1">Title</label> <input type="text" value={
    newNote.title
  } onChange={(e) => setNewNote({ ...newNote, title: e.target.value
  })
  } placeholder="Enter note title..." className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" /> </div> <div> <label className="block text-sm font-medium text-foreground mb-1">Content</label> <textarea value={
    newNote.content
  } onChange={(e) => setNewNote({ ...newNote, content: e.target.value
  })
  } placeholder="Enter medical note content..." className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows={6
  } /> </div> <div className="flex space-x-2">
    <Button onClick={
    handleCreateNote
  }>
    <Save className="w-4 h-4 mr-1" /> Save Note
    </Button>
    <Button variant="outline" onClick={() => setIsCreating(false)
  }>
    <X className="w-4 h-4 mr-1" /> Cancel
    </Button> </div>
    </CardContent>
    </Card> );
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card className="glass border-0 shadow-xl">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
    <FileText className="w-6 h-6 text-white" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary">Medical Notes
    </CardTitle> <p className="macos-text-secondary">Create and manage patient medical notes</p> </div> </div>
    <Button variant="glass" onClick={() => setIsCreating(true)
  } className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> New Note
    </Button> </div>
    </CardHeader>
    </Card> {/* Search and Filter */
  }
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex flex-col sm:flex-row gap-4"> <div className="flex-1 relative">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 macos-text-tertiary w-4 h-4" />
    <Input type="text" placeholder="Search notes by title, content, patient, or tags..." value={
    searchTerm
  } onChange={(e) => setSearchTerm(e.target.value)
  } className="pl-10" variant="glass" /> </div> <div className="flex items-center space-x-3">
    <Filter className="w-4 h-4 macos-text-secondary" /> <select value={
    filterPatient
  } onChange={(e) => setFilterPatient(e.target.value)
  } className="glass border-0 rounded-xl px-4 py-2 text-sm macos-text-primary focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" > <option value="all">All Patients</option> {
    patients.map(patient => ( <option key={
    patient.id
  } value={
    patient.id
  }>{
    patient.name
  }</option> ))
  } </select> </div> </div>
    </CardContent>
    </Card> {/* Create Form */
  } {
    isCreating && renderCreateForm()
  } {/* Notes List */
  } <div className="space-y-4"> {
    filteredNotes.length > 0 ? ( filteredNotes.map(renderNoteCard) ) : (
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <FileText className="w-8 h-8 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2"> No medical notes found </h3> <p className="macos-text-secondary mb-4"> {
    searchTerm || filterPatient !== 'all' ? "No notes match your search criteria." : "You haven't created any medical notes yet."
  } </p> {(!searchTerm && filterPatient === 'all') && (
    <Button variant="glass" onClick={() => setIsCreating(true)
  } className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Create Your First Note
    </Button> )
  }
    </CardContent>
    </Card> )
  } </div> </div> </div> );
  };
  export default MedicalNotes;
