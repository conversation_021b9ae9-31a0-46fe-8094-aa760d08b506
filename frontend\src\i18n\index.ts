import i18n from 'i18next';
  import {
    initReactI18next
  } from 'react-i18next';
  import LanguageDetector from 'i18next-browser-languagedetector';
// Import translation files
import enTranslations from './locales/en.json';
import arTranslations from './locales/ar.json';
  const resources = {
    en: {
    translation: enTranslations,
  }, ar: {
    translation: arTranslations,
  },
  };
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: import.meta.env.DEV,
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
    react: {
      useSuspense: false,
    },
  });
  export default i18n;
