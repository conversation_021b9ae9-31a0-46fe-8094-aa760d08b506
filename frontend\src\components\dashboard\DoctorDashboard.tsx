/**
 * Doctor Dashboard Component
 * Main dashboard interface for doctors with appointments, patients, and quick actions
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import {
  Calendar,
  Users,
  Clock,
  Activity,
  Stethoscope,
  FileText,
  Plus,
  Search,
  Filter,
  Bell,
  TrendingUp,
  Heart,
  Brain
} from 'lucide-react';

import type { RootState, AppDispatch } from '../../store/index';
import DashboardLayout from '../../shared/components/layouts/DashboardLayout';
import MetricCard from '../../shared/components/data-display/MetricCard';
import DataTable from '../ui/DataTable';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/badge';

interface Appointment {
  id: number;
  appointment_id: string;
  patient: {
    user: {
      full_name: string;
    };
    patient_id: string;
  };
  appointment_date: string;
  appointment_time: string;
  appointment_type: string;
  status: string;
  reason_for_visit: string;
}

interface DashboardMetrics {
  todayAppointments: number;
  totalPatients: number;
  pendingReports: number;
  completedToday: number;
}

const DoctorDashboard: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);

  // State management
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    todayAppointments: 0,
    totalPatients: 0,
    pendingReports: 0,
    completedToday: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setMetrics({
        todayAppointments: 12,
        totalPatients: 156,
        pendingReports: 8,
        completedToday: 7
      });

      setAppointments([
        {
          id: 1,
          appointment_id: 'APT001',
          patient: {
            user: { full_name: 'John Doe' },
            patient_id: 'P000001'
          },
          appointment_date: '2025-01-13',
          appointment_time: '09:00',
          appointment_type: 'consultation',
          status: 'scheduled',
          reason_for_visit: 'Regular checkup'
        },
        {
          id: 2,
          appointment_id: 'APT002',
          patient: {
            user: { full_name: 'Jane Smith' },
            patient_id: 'P000002'
          },
          appointment_date: '2025-01-13',
          appointment_time: '10:30',
          appointment_type: 'follow_up',
          status: 'confirmed',
          reason_for_visit: 'Follow-up for diabetes management'
        }
      ]);

      setLoading(false);
    }, 1000);
  }, []);

  // Quick action items
  const quickActions = [
    {
      id: 'new-appointment',
      title: t('common.newAppointment'),
      description: t('common.scheduleNewAppointment'),
      icon: Calendar,
      onClick: () => console.log('New appointment'),
    },
    {
      id: 'patient-search',
      title: t('common.findPatient'),
      description: t('common.searchPatientRecords'),
      icon: Search,
      onClick: () => console.log('Search patients'),
    },
    {
      id: 'prescriptions',
      title: t('common.prescriptions'),
      description: t('common.managePrescriptions'),
      icon: FileText,
      onClick: () => console.log('Manage prescriptions'),
      badge: '5',
    },
    {
      id: 'lab-results',
      title: t('common.labResults'),
      description: t('common.reviewLatestLabReports'),
      icon: Activity,
      onClick: () => console.log('View lab results'),
      badge: '3',
    },
  ];

  // Table columns for appointments
  const appointmentColumns = [
    {
      key: 'appointment_time',
      title: t('common.time'),
      sortable: true,
      width: '100px',
      render: (value: string) => (
        <span className="font-mono text-sm">
          {new Date(`2000-01-01T${value}`).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </span>
      ),
    },
    {
      key: 'patient',
      title: t('common.patient'),
      sortable: true,
      render: (patient: any) => (
        <div>
          <p className="font-medium">{patient.user.full_name}</p>
          <p className="text-sm text-gray-500">{patient.patient_id}</p>
        </div>
      ),
    },
    {
      key: 'appointment_type',
      title: t('common.type'),
      sortable: true,
      render: (value: string) => (
        <Badge variant="outline" className="capitalize">
          {value.replace('_', ' ')}
        </Badge>
      ),
    },
    {
      key: 'status',
      title: t('common.status'),
      sortable: true,
      render: (value: string) => (
        <Badge
          variant={
            value === 'completed'
              ? 'default'
              : value === 'confirmed'
              ? 'secondary'
              : value === 'scheduled'
              ? 'outline'
              : 'destructive'
          }
          className="capitalize"
        >
          {value.replace('_', ' ')}
        </Badge>
      ),
    },
    {
      key: 'reason_for_visit',
      title: t('common.reason'),
      render: (value: string) => (
        <span className="text-sm" title={value}>
          {value.length > 30 ? `${value.substring(0, 30)}...` : value}
        </span>
      ),
    },
  ];

  return (
    <DashboardLayout
      title={t('dashboard.doctorDashboard')}
      subtitle={`Welcome back, Dr. ${user?.full_name || 'Doctor'}`}
      description={t('dashboard.doctorDashboardDescription')}
      icon={Stethoscope}
      iconColor="feature-blue"
    >
      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard
          label={t('dashboard.todayAppointments')}
          value={metrics.todayAppointments.toString()}
          icon={Calendar}
          color="blue"
          trend="up"
          change="+2"
          loading={loading}
        />
        <MetricCard
          label={t('dashboard.totalPatients')}
          value={metrics.totalPatients.toString()}
          icon={Users}
          color="green"
          trend="up"
          change="+5"
          loading={loading}
        />
        <MetricCard
          label={t('dashboard.pendingReports')}
          value={metrics.pendingReports.toString()}
          icon={FileText}
          color="orange"
          trend="down"
          change="-1"
          loading={loading}
        />
        <MetricCard
          label={t('dashboard.completedToday')}
          value={metrics.completedToday.toString()}
          icon={TrendingUp}
          color="purple"
          trend="up"
          change="+3"
          loading={loading}
        />
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">{t('dashboard.quickActions')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <div
              key={action.id}
              className="relative p-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg hover:bg-white/20 transition-all cursor-pointer"
              onClick={action.onClick}
            >
              {action.badge && (
                <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
                  {action.badge}
                </div>
              )}
              <action.icon className="w-8 h-8 text-blue-400 mb-3" />
              <h4 className="font-medium mb-1">{action.title}</h4>
              <p className="text-sm text-gray-400">{action.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Today's Appointments */}
      <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">{t('dashboard.todayAppointments')}</h3>
          <div className="flex items-center gap-3">
            <Input
              placeholder={t('common.searchAppointments')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              {t('common.filter')}
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              {t('common.newAppointment')}
            </Button>
          </div>
        </div>

        <DataTable
          data={appointments}
          columns={appointmentColumns}
          loading={loading}
          onSearch={(term) => setSearchTerm(term)}
          actions={{
            title: t('common.actions'),
            render: (appointment: Appointment) => (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  {t('common.view')}
                </Button>
                <Button variant="outline" size="sm">
                  {t('common.edit')}
                </Button>
              </div>
            ),
          }}
        />
      </div>
    </DashboardLayout>
  );
};

export default DoctorDashboard;