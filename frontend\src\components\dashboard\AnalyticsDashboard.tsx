import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import useApi from '../../shared/hooks/useApi';
import departmentService from '../../services/departmentService';
import { Loader2, AlertCircle } from 'lucide-react';
import {
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
} from 'recharts';
// Mock data for charts

const appointmentData = [ {
    month: 'Jan', appointments: 120, completed: 110, cancelled: 10
  }, {
    month: 'Feb', appointments: 135, completed: 125, cancelled: 10
  }, {
    month: 'Mar', appointments: 148, completed: 140, cancelled: 8
  }, {
    month: 'Apr', appointments: 162, completed: 155, cancelled: 7
  }, {
    month: 'May', appointments: 178, completed: 170, cancelled: 8
  }, {
    month: 'Jun', appointments: 195, completed: 185, cancelled: 10
  }, ];
  const revenueData = [ {
    month: 'Jan', revenue: 45000, expenses: 32000
  }, {
    month: 'Feb', revenue: 52000, expenses: 35000
  }, {
    month: 'Mar', revenue: 48000, expenses: 33000
  }, {
    month: 'Apr', revenue: 61000, expenses: 38000
  }, {
    month: 'May', revenue: 55000, expenses: 36000
  }, {
    month: 'Jun', revenue: 67000, expenses: 40000
  }, ];
  const patientDemographics = [ {
    name: '0-18', value: 15, color: '#8884d8'
  }, {
    name: '19-35', value: 25, color: '#82ca9d'
  }, {
    name: '36-50', value: 30, color: '#ffc658'
  }, {
    name: '51-65', value: 20, color: '#ff7300'
  }, {
    name: '65+', value: 10, color: '#00ff00'
  }, ];
  const AnalyticsDashboard: React.FC = () => {
    const { t } = useTranslation();

    // Fetch department statistics
    const {
      data: departments,
      loading: departmentsLoading,
      error: departmentsError,
    } = useApi(() => departmentService.getDepartments(), {
      immediate: true,
    });

    // Transform department data for charts
    const departmentStats = departments?.map((dept: any) => ({
      department: dept.name,
      patients: dept.patient_count || 0,
      revenue: dept.revenue_this_month || 0,
    })) || [];

    // Show loading state
    if (departmentsLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-muted-foreground">{t('common.loading')}...</p>
          </div>
        </div>
      );
    }

    // Show error state
    if (departmentsError) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-2">{t('analytics.errorLoadingData')}</p>
          </div>
        </div>
      );
    }

  return ( <div className="space-y-6"> {/* Key Metrics */
  } <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"> <div className="bg-background p-6 rounded-lg shadow"> <div className="flex items-center"> <div className="p-2 bg-blue-100 rounded-lg"> <svg className="w-6 h-6 text-sky-700 dark:text-sky-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{
    t('dashboard.totalPatients')
  }</p> <p className="text-2xl font-bold text-foreground">1,247</p> <p className="text-sm text-emerald-700 dark:text-emerald-400">+12% from last month</p> </div> </div> </div> <div className="bg-background p-6 rounded-lg shadow"> <div className="flex items-center"> <div className="p-2 bg-green-100 rounded-lg"> <svg className="w-6 h-6 text-emerald-700 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{
    t('dashboard.monthlyRevenue')
  }</p> <p className="text-2xl font-bold text-foreground">$67,000</p> <p className="text-sm text-emerald-700 dark:text-emerald-400">+8% from last month</p> </div> </div> </div> <div className="bg-background p-6 rounded-lg shadow"> <div className="flex items-center"> <div className="p-2 bg-yellow-100 rounded-lg"> <svg className="w-6 h-6 text-amber-700 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{
    t('dashboard.appointmentsToday')
  }</p> <p className="text-2xl font-bold text-foreground">24</p> <p className="text-sm text-sky-700 dark:text-sky-400">3 pending confirmations</p> </div> </div> </div> <div className="bg-background p-6 rounded-lg shadow"> <div className="flex items-center"> <div className="p-2 bg-red-100 rounded-lg"> <svg className="w-6 h-6 text-rose-700 dark:text-rose-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{
    t('dashboard.satisfactionRate')
  }</p> <p className="text-2xl font-bold text-foreground">94.5%</p> <p className="text-sm text-emerald-700 dark:text-emerald-400">+2.1% from last month</p> </div> </div> </div> </div> {/* Charts Row 1 */
  } <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Appointments Trend */
  } <div className="bg-background p-6 rounded-lg shadow"> <h3 className="text-lg font-semibold text-foreground mb-4">{
    t('dashboard.appointmentsTrend') || 'Appointments Trend'
  }</h3>
    <ResponsiveContainer width="100%" height={300
  }>
    <LineChart data={
    appointmentData
  }>
    <CartesianGrid strokeDasharray="3 3" />
    <XAxis dataKey="month" />
    <YAxis />
    <Tooltip />
    <Legend />
    <Line type="monotone" dataKey="appointments" stroke="#3B82F6" strokeWidth={2
  } />
    <Line type="monotone" dataKey="completed" stroke="#10B981" strokeWidth={2
  } />
    <Line type="monotone" dataKey="cancelled" stroke="#EF4444" strokeWidth={2
  } />
    </LineChart>
    </ResponsiveContainer> </div> {/* Revenue vs Expenses */
  } <div className="bg-background p-6 rounded-lg shadow"> <h3 className="text-lg font-semibold text-foreground mb-4">{
    t('dashboard.revenueVsExpenses')
  }</h3>
    <ResponsiveContainer width="100%" height={300
  }>
    <AreaChart data={
    revenueData
  }>
    <CartesianGrid strokeDasharray="3 3" />
    <XAxis dataKey="month" />
    <YAxis />
    <Tooltip formatter={(value) => [`$${
    value.toLocaleString()
  }`, '']
  } />
    <Legend />
    <Area type="monotone" dataKey="revenue" stackId="1" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.6
  } />
    <Area type="monotone" dataKey="expenses" stackId="2" stroke="#EF4444" fill="#EF4444" fillOpacity={0.6
  } />
    </AreaChart>
    </ResponsiveContainer> </div> </div> {/* Charts Row 2 */
  } <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Patient Demographics */
  } <div className="bg-background p-6 rounded-lg shadow"> <h3 className="text-lg font-semibold text-foreground mb-4">{
    t('dashboard.patientDemographics')
  } ({
    t('dashboard.ageGroups')
  })</h3>
    <ResponsiveContainer width="100%" height={300
  }>
    <PieChart>
    <Pie data={
    patientDemographics
  } cx="50%" cy="50%" labelLine={
    false
  } label={({
    name, percent
  }) => `${
    name
  } ${(typeof percent === 'number' ? (percent * 100).toFixed(0) : '0')
  }%`
  } outerRadius={80
  } fill="#8884d8" dataKey="value" > {
    patientDemographics.map((entry, index) => (
    <Cell key={`cell-${
    index
  }`
  } fill={
    entry.color
  } /> ))
  }
    </Pie>
    <Tooltip />
    </PieChart>
    </ResponsiveContainer> </div> {/* Department Statistics */
  } <div className="bg-background p-6 rounded-lg shadow"> <h3 className="text-lg font-semibold text-foreground mb-4">{
    t('dashboard.departmentPerformance')
  }</h3>
    <ResponsiveContainer width="100%" height={300
  }>
    <BarChart data={
    departmentStats
  }>
    <CartesianGrid strokeDasharray="3 3" />
    <XAxis dataKey="department" />
    <YAxis yAxisId="left" />
    <YAxis yAxisId="right" orientation="right" />
    <Tooltip />
    <Legend />
    <Bar yAxisId="left" dataKey="patients" fill="#3B82F6" name={
    t('dashboard.chartPatients')
  } />
    <Bar yAxisId="right" dataKey="revenue" fill="#10B981" name={
    t('dashboard.chartRevenue')
  } />
    </BarChart>
    </ResponsiveContainer> </div> </div> {/* Recent Activity */
  } <div className="bg-background rounded-lg shadow"> <div className="p-6 border-b border-border"> <h3 className="text-lg font-semibold text-foreground">{
    t('dashboard.recentActivity')
  }</h3> </div> <div className="p-6"> <div className="space-y-4"> {[ {
    type: 'appointment', message: t('dashboard.newAppointmentScheduled'), time: `2 ${
    t('dashboard.minutesAgo')
  }`, icon: '📅'
  }, {
    type: 'patient', message: t('dashboard.patientRegistered'), time: `15 ${
    t('dashboard.minutesAgo')
  }`, icon: '👤'
  }, {
    type: 'payment', message: t('dashboard.paymentReceived'), time: `1 ${
    t('dashboard.hourAgo')
  }`, icon: '💰'
  }, {
    type: 'lab', message: t('dashboard.labResultsReady'), time: `2 ${
    t('dashboard.hoursAgo')
  }`, icon: '🧪'
  }, {
    type: 'prescription', message: t('dashboard.prescriptionIssued'), time: `3 ${
    t('dashboard.hoursAgo')
  }`, icon: '💊'
  }, ].map((activity, index) => ( <div key={
    index
  } className="flex items-center space-x-3 p-3 bg-muted rounded-lg"> <span className="text-2xl">{
    activity.icon
  }</span> <div className="flex-1"> <p className="text-sm font-medium text-foreground">{
    activity.message
  }</p> <p className="text-xs text-gray-500">{
    activity.time
  }</p> </div> </div> ))
  } </div> </div> </div> </div> );
  };
  export default AnalyticsDashboard;
