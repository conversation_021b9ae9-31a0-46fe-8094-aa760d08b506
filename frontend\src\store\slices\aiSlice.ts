/**
 * AI Slice for Redux Store
 * Manages AI-related state including chat, diagnosis, and multi-agent workflows
 */
import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';

// AI Chat Types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: {
    confidence?: number;
    sources?: string[];
    type?: 'diagnosis' | 'treatment' | 'general';
  };
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  patientId?: string;
  context?: string;
  createdAt: string;
  updatedAt: string;
}

// AI Diagnosis Types
export interface DiagnosisRequest {
  patientId: string;
  symptoms: string[];
  vitalSigns?: {
    temperature?: number;
    bloodPressure?: string;
    heartRate?: number;
    respiratoryRate?: number;
  };
  medicalHistory?: string[];
  currentMedications?: string[];
}

export interface DiagnosisResult {
  id: string;
  patientId: string;
  primaryDiagnosis: {
    condition: string;
    confidence: number;
    icd10Code?: string;
  };
  differentialDiagnoses: Array<{
    condition: string;
    confidence: number;
    icd10Code?: string;
  }>;
  recommendedTests: string[];
  treatmentSuggestions: string[];
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  createdAt: string;
  aiModel: string;
  reasoning: string;
}

// Multi-Agent Types
export interface Agent {
  id: string;
  name: string;
  type: 'diagnosis' | 'treatment' | 'triage' | 'pharmacy' | 'coordinator';
  status: 'idle' | 'active' | 'processing' | 'completed' | 'error';
  capabilities: string[];
  currentTask?: string;
}

export interface MultiAgentWorkflow {
  id: string;
  name: string;
  patientId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  agents: Agent[];
  results: Record<string, any>;
  startedAt: string;
  completedAt?: string;
  progress: number;
}

// AI State Interface
export interface AIState {
  // Chat state
  chatSessions: ChatSession[];
  activeChatSession: string | null;
  chatLoading: boolean;
  chatError: string | null;
  
  // Diagnosis state
  diagnosisRequests: DiagnosisRequest[];
  diagnosisResults: DiagnosisResult[];
  diagnosisLoading: boolean;
  diagnosisError: string | null;
  
  // Multi-agent state
  workflows: MultiAgentWorkflow[];
  activeWorkflow: string | null;
  workflowLoading: boolean;
  workflowError: string | null;
  
  // AI Configuration
  aiConfig: {
    model: string;
    temperature: number;
    maxTokens: number;
    enableMultiAgent: boolean;
  };
  
  // General AI state
  isAIEnabled: boolean;
  aiStatus: 'online' | 'offline' | 'maintenance';
}

// Initial state
const initialState: AIState = {
  chatSessions: [],
  activeChatSession: null,
  chatLoading: false,
  chatError: null,
  
  diagnosisRequests: [],
  diagnosisResults: [],
  diagnosisLoading: false,
  diagnosisError: null,
  
  workflows: [],
  activeWorkflow: null,
  workflowLoading: false,
  workflowError: null,
  
  aiConfig: {
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2000,
    enableMultiAgent: true,
  },
  
  isAIEnabled: true,
  aiStatus: 'online',
};

// Async thunks
export const sendChatMessage = createAsyncThunk(
  'ai/sendChatMessage',
  async (payload: { sessionId: string; message: string; patientId?: string }) => {
    // This would call your AI service
    const response = await fetch('/api/ai/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
    return response.json();
  }
);

export const sendMessage = createAsyncThunk(
  'ai/sendMessage',
  async (payload: { message: string; conversation_type: string; patient_id?: number; conversation_id?: string }) => {
    const response = await fetch('/api/ai/chat/send', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
    return response.json();
  }
);

export const requestDiagnosis = createAsyncThunk(
  'ai/requestDiagnosis',
  async (request: DiagnosisRequest) => {
    const response = await fetch('/api/ai/diagnosis', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    return response.json();
  }
);

export const fetchHealthStatus = createAsyncThunk(
  'ai/fetchHealthStatus',
  async () => {
    const response = await fetch('/api/ai/health', {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });
    return response.json();
  }
);

export const fetchConversations = createAsyncThunk(
  'ai/fetchConversations',
  async () => {
    const response = await fetch('/api/ai/conversations', {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });
    return response.json();
  }
);

export const startMultiAgentWorkflow = createAsyncThunk(
  'ai/startMultiAgentWorkflow',
  async (payload: { patientId: string; workflowType: string }) => {
    const response = await fetch('/api/ai/multi-agent/start', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
    return response.json();
  }
);

// AI Slice
const aiSlice = createSlice({
  name: 'ai',
  initialState,
  reducers: {
    // Chat actions
    createChatSession: (state, action: PayloadAction<{ title: string; patientId?: string }>) => {
      const newSession: ChatSession = {
        id: Date.now().toString(),
        title: action.payload.title,
        messages: [],
        patientId: action.payload.patientId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      state.chatSessions.push(newSession);
      state.activeChatSession = newSession.id;
    },
    
    setActiveChatSession: (state, action: PayloadAction<string>) => {
      state.activeChatSession = action.payload;
    },
    
    addMessageToSession: (state, action: PayloadAction<{ sessionId: string; message: ChatMessage }>) => {
      const session = state.chatSessions.find(s => s.id === action.payload.sessionId);
      if (session) {
        session.messages.push(action.payload.message);
        session.updatedAt = new Date().toISOString();
      }
    },
    
    clearChatError: (state) => {
      state.chatError = null;
    },
    
    // Diagnosis actions
    clearDiagnosisError: (state) => {
      state.diagnosisError = null;
    },
    
    // Workflow actions
    setActiveWorkflow: (state, action: PayloadAction<string>) => {
      state.activeWorkflow = action.payload;
    },
    
    updateWorkflowProgress: (state, action: PayloadAction<{ workflowId: string; progress: number }>) => {
      const workflow = state.workflows.find(w => w.id === action.payload.workflowId);
      if (workflow) {
        workflow.progress = action.payload.progress;
      }
    },
    
    clearWorkflowError: (state) => {
      state.workflowError = null;
    },
    
    // Configuration actions
    updateAIConfig: (state, action: PayloadAction<Partial<AIState['aiConfig']>>) => {
      state.aiConfig = { ...state.aiConfig, ...action.payload };
    },
    
    setAIStatus: (state, action: PayloadAction<AIState['aiStatus']>) => {
      state.aiStatus = action.payload;
    },
    
    toggleAI: (state) => {
      state.isAIEnabled = !state.isAIEnabled;
    },
  },
  
  extraReducers: (builder) => {
    // Chat message handling
    builder
      .addCase(sendChatMessage.pending, (state) => {
        state.chatLoading = true;
        state.chatError = null;
      })
      .addCase(sendChatMessage.fulfilled, (state, action) => {
        state.chatLoading = false;
        const { sessionId, message } = action.payload;
        const session = state.chatSessions.find(s => s.id === sessionId);
        if (session) {
          session.messages.push(message);
          session.updatedAt = new Date().toISOString();
        }
      })
      .addCase(sendChatMessage.rejected, (state, action) => {
        state.chatLoading = false;
        state.chatError = action.error.message || 'Failed to send message';
      });
    
    // Diagnosis handling
    builder
      .addCase(requestDiagnosis.pending, (state) => {
        state.diagnosisLoading = true;
        state.diagnosisError = null;
      })
      .addCase(requestDiagnosis.fulfilled, (state, action) => {
        state.diagnosisLoading = false;
        state.diagnosisResults.push(action.payload);
      })
      .addCase(requestDiagnosis.rejected, (state, action) => {
        state.diagnosisLoading = false;
        state.diagnosisError = action.error.message || 'Failed to get diagnosis';
      });
    
    // Multi-agent workflow handling
    builder
      .addCase(startMultiAgentWorkflow.pending, (state) => {
        state.workflowLoading = true;
        state.workflowError = null;
      })
      .addCase(startMultiAgentWorkflow.fulfilled, (state, action) => {
        state.workflowLoading = false;
        state.workflows.push(action.payload);
        state.activeWorkflow = action.payload.id;
      })
      .addCase(startMultiAgentWorkflow.rejected, (state, action) => {
        state.workflowLoading = false;
        state.workflowError = action.error.message || 'Failed to start workflow';
      })
      // Fetch health status
      .addCase(fetchHealthStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchHealthStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.aiStatus = action.payload.status || 'online';
      })
      .addCase(fetchHealthStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch health status';
      })
      // Fetch conversations
      .addCase(fetchConversations.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.loading = false;
        state.chatSessions = action.payload || [];
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch conversations';
      });
  },
});

// Export actions
export const {
  createChatSession,
  setActiveChatSession,
  addMessageToSession,
  clearChatError,
  clearDiagnosisError,
  setActiveWorkflow,
  updateWorkflowProgress,
  clearWorkflowError,
  updateAIConfig,
  setAIStatus,
  toggleAI,
} = aiSlice.actions;

// Export reducer
export default aiSlice.reducer;